
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Python312\\Scripts"
        - "C:\\Python312"
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\WINDOWS\\System32\\OpenSSH"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\dotnet"
        - "C:\\MinGW\\bin"
        - "C:\\Program Files\\ffmpeg-6.1.1-essentials_build\\bin"
        - "C:\\Program Files\\nodejs"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files\\Graphviz\\bin"
        - "C:\\maven\\apache-maven-3.9.9-bin\\apache-maven-3.9.9\\bin"
        - "C:\\Users\\<USER>\\.jdks\\corretto-17.0.8\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\WINDOWS\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ghcup\\bin"
        - "C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.3\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.14.10+8b8e13593 for .NET Framework
      Build started 2025-08-30 2:47:31 PM.
      
      Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\C++\\automata\\CMakeFiles\\4.1.1\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> C:\\Users\\<USER>\\OneDrive\\Desktop\\C++\\automata\\CMakeFiles\\4.1.1\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\C++\\automata\\CMakeFiles\\4.1.1\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:01.19
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        C:/Users/<USER>/OneDrive/Desktop/C++/automata/CMakeFiles/4.1.1/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:103 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "C:/Python312/Scripts/"
      - "C:/Python312/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/dotnet/"
      - "C:/MinGW/bin/"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files/Graphviz/bin/"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ghcup/bin/"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Python312\\Scripts"
        - "C:\\Python312"
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\WINDOWS\\System32\\OpenSSH"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\dotnet"
        - "C:\\MinGW\\bin"
        - "C:\\Program Files\\ffmpeg-6.1.1-essentials_build\\bin"
        - "C:\\Program Files\\nodejs"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files\\Graphviz\\bin"
        - "C:\\maven\\apache-maven-3.9.9-bin\\apache-maven-3.9.9\\bin"
        - "C:\\Users\\<USER>\\.jdks\\corretto-17.0.8\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\WINDOWS\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ghcup\\bin"
        - "C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.3\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:104 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lld-link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "C:/Python312/Scripts/"
      - "C:/Python312/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/dotnet/"
      - "C:/MinGW/bin/"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files/Graphviz/bin/"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ghcup/bin/"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link"
      - "C:/Users/<USER>/bin/lld-link.com"
      - "C:/Users/<USER>/bin/lld-link.exe"
      - "C:/Users/<USER>/bin/lld-link"
      - "C:/Program Files/Git/mingw64/bin/lld-link.com"
      - "C:/Program Files/Git/mingw64/bin/lld-link.exe"
      - "C:/Program Files/Git/mingw64/bin/lld-link"
      - "C:/Program Files/Git/usr/local/bin/lld-link.com"
      - "C:/Program Files/Git/usr/local/bin/lld-link.exe"
      - "C:/Program Files/Git/usr/local/bin/lld-link"
      - "C:/Program Files/Git/usr/bin/lld-link.com"
      - "C:/Program Files/Git/usr/bin/lld-link.exe"
      - "C:/Program Files/Git/usr/bin/lld-link"
      - "C:/Python312/Scripts/lld-link.com"
      - "C:/Python312/Scripts/lld-link.exe"
      - "C:/Python312/Scripts/lld-link"
      - "C:/Python312/lld-link.com"
      - "C:/Python312/lld-link.exe"
      - "C:/Python312/lld-link"
      - "C:/Program Files/Oculus/Support/oculus-runtime/lld-link.com"
      - "C:/Program Files/Oculus/Support/oculus-runtime/lld-link.exe"
      - "C:/Program Files/Oculus/Support/oculus-runtime/lld-link"
      - "C:/Windows/System32/lld-link.com"
      - "C:/Windows/System32/lld-link.exe"
      - "C:/Windows/System32/lld-link"
      - "C:/Windows/lld-link.com"
      - "C:/Windows/lld-link.exe"
      - "C:/Windows/lld-link"
      - "C:/Windows/System32/wbem/lld-link.com"
      - "C:/Windows/System32/wbem/lld-link.exe"
      - "C:/Windows/System32/wbem/lld-link"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link"
      - "C:/Windows/System32/OpenSSH/lld-link.com"
      - "C:/Windows/System32/OpenSSH/lld-link.exe"
      - "C:/Windows/System32/OpenSSH/lld-link"
      - "C:/Program Files/Git/cmd/lld-link.com"
      - "C:/Program Files/Git/cmd/lld-link.exe"
      - "C:/Program Files/Git/cmd/lld-link"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/lld-link.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/lld-link.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/lld-link"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link"
      - "C:/Program Files/dotnet/lld-link.com"
      - "C:/Program Files/dotnet/lld-link.exe"
      - "C:/Program Files/dotnet/lld-link"
      - "C:/MinGW/bin/lld-link.com"
      - "C:/MinGW/bin/lld-link.exe"
      - "C:/MinGW/bin/lld-link"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/lld-link.com"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/lld-link.exe"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/lld-link"
      - "C:/Program Files/nodejs/lld-link.com"
      - "C:/Program Files/nodejs/lld-link.exe"
      - "C:/Program Files/nodejs/lld-link"
      - "C:/ProgramData/chocolatey/bin/lld-link.com"
      - "C:/ProgramData/chocolatey/bin/lld-link.exe"
      - "C:/ProgramData/chocolatey/bin/lld-link"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link.com"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link"
      - "C:/Program Files/Graphviz/bin/lld-link.com"
      - "C:/Program Files/Graphviz/bin/lld-link.exe"
      - "C:/Program Files/Graphviz/bin/lld-link"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/lld-link.com"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/lld-link.exe"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/lld-link"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/lld-link.com"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/lld-link.exe"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/lld-link"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link"
      - "C:/Program Files/CMake/bin/lld-link.com"
      - "C:/Program Files/CMake/bin/lld-link.exe"
      - "C:/Program Files/CMake/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/lld-link"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link"
      - "C:/ghcup/bin/lld-link.com"
      - "C:/ghcup/bin/lld-link.exe"
      - "C:/ghcup/bin/lld-link"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/lld-link.com"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/lld-link.exe"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/lld-link"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/lld-link.com"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/lld-link.exe"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/lld-link"
      - "C:/Program Files/Git/usr/bin/vendor_perl/lld-link.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/lld-link.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/lld-link"
      - "C:/Program Files/Git/usr/bin/core_perl/lld-link.com"
      - "C:/Program Files/Git/usr/bin/core_perl/lld-link.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/lld-link"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Python312\\Scripts"
        - "C:\\Python312"
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\WINDOWS\\System32\\OpenSSH"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\dotnet"
        - "C:\\MinGW\\bin"
        - "C:\\Program Files\\ffmpeg-6.1.1-essentials_build\\bin"
        - "C:\\Program Files\\nodejs"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files\\Graphviz\\bin"
        - "C:\\maven\\apache-maven-3.9.9-bin\\apache-maven-3.9.9\\bin"
        - "C:\\Users\\<USER>\\.jdks\\corretto-17.0.8\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\WINDOWS\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ghcup\\bin"
        - "C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.3\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "C:/Python312/Scripts/"
      - "C:/Python312/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/dotnet/"
      - "C:/MinGW/bin/"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files/Graphviz/bin/"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ghcup/bin/"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Python312\\Scripts"
        - "C:\\Python312"
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\WINDOWS\\System32\\OpenSSH"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\dotnet"
        - "C:\\MinGW\\bin"
        - "C:\\Program Files\\ffmpeg-6.1.1-essentials_build\\bin"
        - "C:\\Program Files\\nodejs"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files\\Graphviz\\bin"
        - "C:\\maven\\apache-maven-3.9.9-bin\\apache-maven-3.9.9\\bin"
        - "C:\\Users\\<USER>\\.jdks\\corretto-17.0.8\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\WINDOWS\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ghcup\\bin"
        - "C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.3\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_MT"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "mt"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "C:/Python312/Scripts/"
      - "C:/Python312/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/dotnet/"
      - "C:/MinGW/bin/"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files/Graphviz/bin/"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ghcup/bin/"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/mt.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/mt.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/mt"
      - "C:/Users/<USER>/bin/mt.com"
      - "C:/Users/<USER>/bin/mt.exe"
      - "C:/Users/<USER>/bin/mt"
      - "C:/Program Files/Git/mingw64/bin/mt.com"
      - "C:/Program Files/Git/mingw64/bin/mt.exe"
      - "C:/Program Files/Git/mingw64/bin/mt"
      - "C:/Program Files/Git/usr/local/bin/mt.com"
      - "C:/Program Files/Git/usr/local/bin/mt.exe"
      - "C:/Program Files/Git/usr/local/bin/mt"
      - "C:/Program Files/Git/usr/bin/mt.com"
      - "C:/Program Files/Git/usr/bin/mt.exe"
      - "C:/Program Files/Git/usr/bin/mt"
      - "C:/Python312/Scripts/mt.com"
      - "C:/Python312/Scripts/mt.exe"
      - "C:/Python312/Scripts/mt"
      - "C:/Python312/mt.com"
      - "C:/Python312/mt.exe"
      - "C:/Python312/mt"
      - "C:/Program Files/Oculus/Support/oculus-runtime/mt.com"
      - "C:/Program Files/Oculus/Support/oculus-runtime/mt.exe"
      - "C:/Program Files/Oculus/Support/oculus-runtime/mt"
      - "C:/Windows/System32/mt.com"
      - "C:/Windows/System32/mt.exe"
      - "C:/Windows/System32/mt"
      - "C:/Windows/mt.com"
      - "C:/Windows/mt.exe"
      - "C:/Windows/mt"
      - "C:/Windows/System32/wbem/mt.com"
      - "C:/Windows/System32/wbem/mt.exe"
      - "C:/Windows/System32/wbem/mt"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt"
      - "C:/Windows/System32/OpenSSH/mt.com"
      - "C:/Windows/System32/OpenSSH/mt.exe"
      - "C:/Windows/System32/OpenSSH/mt"
      - "C:/Program Files/Git/cmd/mt.com"
      - "C:/Program Files/Git/cmd/mt.exe"
      - "C:/Program Files/Git/cmd/mt"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/mt.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/mt.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/mt"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/mt.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/mt.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/mt"
      - "C:/Program Files/dotnet/mt.com"
      - "C:/Program Files/dotnet/mt.exe"
      - "C:/Program Files/dotnet/mt"
      - "C:/MinGW/bin/mt.com"
      - "C:/MinGW/bin/mt.exe"
      - "C:/MinGW/bin/mt"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/mt.com"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/mt.exe"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/mt"
      - "C:/Program Files/nodejs/mt.com"
      - "C:/Program Files/nodejs/mt.exe"
      - "C:/Program Files/nodejs/mt"
      - "C:/ProgramData/chocolatey/bin/mt.com"
      - "C:/ProgramData/chocolatey/bin/mt.exe"
      - "C:/ProgramData/chocolatey/bin/mt"
      - "C:/Program Files/Docker/Docker/resources/bin/mt.com"
      - "C:/Program Files/Docker/Docker/resources/bin/mt.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/mt"
      - "C:/Program Files/Graphviz/bin/mt.com"
      - "C:/Program Files/Graphviz/bin/mt.exe"
      - "C:/Program Files/Graphviz/bin/mt"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/mt.com"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/mt.exe"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/mt"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/mt.com"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/mt.exe"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/mt"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/mt.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/mt.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/mt"
      - "C:/Program Files/CMake/bin/mt.com"
      - "C:/Program Files/CMake/bin/mt.exe"
      - "C:/Program Files/CMake/bin/mt"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/mt"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/mt.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/mt"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt"
      - "C:/ghcup/bin/mt.com"
      - "C:/ghcup/bin/mt.exe"
      - "C:/ghcup/bin/mt"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/mt.com"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/mt.exe"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/mt"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/mt.com"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/mt.exe"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/mt"
      - "C:/Program Files/Git/usr/bin/vendor_perl/mt.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/mt.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/mt"
      - "C:/Program Files/Git/usr/bin/core_perl/mt.com"
      - "C:/Program Files/Git/usr/bin/core_perl/mt.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/mt"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Python312\\Scripts"
        - "C:\\Python312"
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\WINDOWS\\System32\\OpenSSH"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\dotnet"
        - "C:\\MinGW\\bin"
        - "C:\\Program Files\\ffmpeg-6.1.1-essentials_build\\bin"
        - "C:\\Program Files\\nodejs"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files\\Graphviz\\bin"
        - "C:\\maven\\apache-maven-3.9.9-bin\\apache-maven-3.9.9\\bin"
        - "C:\\Users\\<USER>\\.jdks\\corretto-17.0.8\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\WINDOWS\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ghcup\\bin"
        - "C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.3\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lib"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "C:/Python312/Scripts/"
      - "C:/Python312/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/dotnet/"
      - "C:/MinGW/bin/"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files/Graphviz/bin/"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ghcup/bin/"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lib.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lib.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Python312\\Scripts"
        - "C:\\Python312"
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\WINDOWS\\System32\\OpenSSH"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\dotnet"
        - "C:\\MinGW\\bin"
        - "C:\\Program Files\\ffmpeg-6.1.1-essentials_build\\bin"
        - "C:\\Program Files\\nodejs"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files\\Graphviz\\bin"
        - "C:\\maven\\apache-maven-3.9.9-bin\\apache-maven-3.9.9\\bin"
        - "C:\\Users\\<USER>\\.jdks\\corretto-17.0.8\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\WINDOWS\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ghcup\\bin"
        - "C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.3\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineRCCompiler.cmake:40 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC.cmake:574 (enable_language)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC.cmake:547 (__windows_compiler_msvc_enable_rc)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC-CXX.cmake:6 (__windows_compiler_msvc)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXInformation.cmake:48 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_RC_COMPILER"
    description: "RC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "rc"
    candidate_directories:
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "C:/Python312/Scripts/"
      - "C:/Python312/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/dotnet/"
      - "C:/MinGW/bin/"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files/Graphviz/bin/"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ghcup/bin/"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/automata/bin/"
      - "C:/Program Files (x86)/automata/sbin/"
      - "C:/Program Files (x86)/automata/"
    searched_directories:
      - "C:/Users/<USER>/bin/rc.com"
      - "C:/Users/<USER>/bin/rc.exe"
      - "C:/Users/<USER>/bin/rc"
      - "C:/Program Files/Git/mingw64/bin/rc.com"
      - "C:/Program Files/Git/mingw64/bin/rc.exe"
      - "C:/Program Files/Git/mingw64/bin/rc"
      - "C:/Program Files/Git/usr/local/bin/rc.com"
      - "C:/Program Files/Git/usr/local/bin/rc.exe"
      - "C:/Program Files/Git/usr/local/bin/rc"
      - "C:/Program Files/Git/usr/bin/rc.com"
      - "C:/Program Files/Git/usr/bin/rc.exe"
      - "C:/Program Files/Git/usr/bin/rc"
      - "C:/Python312/Scripts/rc.com"
      - "C:/Python312/Scripts/rc.exe"
      - "C:/Python312/Scripts/rc"
      - "C:/Python312/rc.com"
      - "C:/Python312/rc.exe"
      - "C:/Python312/rc"
      - "C:/Program Files/Oculus/Support/oculus-runtime/rc.com"
      - "C:/Program Files/Oculus/Support/oculus-runtime/rc.exe"
      - "C:/Program Files/Oculus/Support/oculus-runtime/rc"
      - "C:/Windows/System32/rc.com"
      - "C:/Windows/System32/rc.exe"
      - "C:/Windows/System32/rc"
      - "C:/Windows/rc.com"
      - "C:/Windows/rc.exe"
      - "C:/Windows/rc"
      - "C:/Windows/System32/wbem/rc.com"
      - "C:/Windows/System32/wbem/rc.exe"
      - "C:/Windows/System32/wbem/rc"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc"
      - "C:/Windows/System32/OpenSSH/rc.com"
      - "C:/Windows/System32/OpenSSH/rc.exe"
      - "C:/Windows/System32/OpenSSH/rc"
      - "C:/Program Files/Git/cmd/rc.com"
      - "C:/Program Files/Git/cmd/rc.exe"
      - "C:/Program Files/Git/cmd/rc"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/rc.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/rc.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/rc"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/rc.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/rc.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/rc"
      - "C:/Program Files/dotnet/rc.com"
      - "C:/Program Files/dotnet/rc.exe"
      - "C:/Program Files/dotnet/rc"
      - "C:/MinGW/bin/rc.com"
      - "C:/MinGW/bin/rc.exe"
      - "C:/MinGW/bin/rc"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/rc.com"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/rc.exe"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/rc"
      - "C:/Program Files/nodejs/rc.com"
      - "C:/Program Files/nodejs/rc.exe"
      - "C:/Program Files/nodejs/rc"
      - "C:/ProgramData/chocolatey/bin/rc.com"
      - "C:/ProgramData/chocolatey/bin/rc.exe"
      - "C:/ProgramData/chocolatey/bin/rc"
      - "C:/Program Files/Docker/Docker/resources/bin/rc.com"
      - "C:/Program Files/Docker/Docker/resources/bin/rc.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/rc"
      - "C:/Program Files/Graphviz/bin/rc.com"
      - "C:/Program Files/Graphviz/bin/rc.exe"
      - "C:/Program Files/Graphviz/bin/rc"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/rc.com"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/rc.exe"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/rc"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/rc.com"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/rc.exe"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/rc"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/rc.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/rc.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/rc"
      - "C:/Program Files/CMake/bin/rc.com"
      - "C:/Program Files/CMake/bin/rc.exe"
      - "C:/Program Files/CMake/bin/rc"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/rc"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/rc.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/rc"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc"
      - "C:/ghcup/bin/rc.com"
      - "C:/ghcup/bin/rc.exe"
      - "C:/ghcup/bin/rc"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/rc.com"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/rc.exe"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/rc"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/rc.com"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/rc.exe"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/rc"
      - "C:/Program Files/Git/usr/bin/vendor_perl/rc.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/rc.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/rc"
      - "C:/Program Files/Git/usr/bin/core_perl/rc.com"
      - "C:/Program Files/Git/usr/bin/core_perl/rc.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/rc"
      - "C:/Program Files/bin/rc.com"
      - "C:/Program Files/bin/rc.exe"
      - "C:/Program Files/bin/rc"
      - "C:/Program Files/sbin/rc.com"
      - "C:/Program Files/sbin/rc.exe"
      - "C:/Program Files/sbin/rc"
      - "C:/Program Files/rc.com"
      - "C:/Program Files/rc.exe"
      - "C:/Program Files/rc"
      - "C:/Program Files (x86)/bin/rc.com"
      - "C:/Program Files (x86)/bin/rc.exe"
      - "C:/Program Files (x86)/bin/rc"
      - "C:/Program Files (x86)/sbin/rc.com"
      - "C:/Program Files (x86)/sbin/rc.exe"
      - "C:/Program Files (x86)/sbin/rc"
      - "C:/Program Files (x86)/rc.com"
      - "C:/Program Files (x86)/rc.exe"
      - "C:/Program Files (x86)/rc"
      - "C:/Program Files/CMake/bin/rc.com"
      - "C:/Program Files/CMake/bin/rc.exe"
      - "C:/Program Files/CMake/bin/rc"
      - "C:/Program Files/CMake/sbin/rc.com"
      - "C:/Program Files/CMake/sbin/rc.exe"
      - "C:/Program Files/CMake/sbin/rc"
      - "C:/Program Files/CMake/rc.com"
      - "C:/Program Files/CMake/rc.exe"
      - "C:/Program Files/CMake/rc"
      - "C:/Program Files (x86)/automata/bin/rc.com"
      - "C:/Program Files (x86)/automata/bin/rc.exe"
      - "C:/Program Files (x86)/automata/bin/rc"
      - "C:/Program Files (x86)/automata/sbin/rc.com"
      - "C:/Program Files (x86)/automata/sbin/rc.exe"
      - "C:/Program Files (x86)/automata/sbin/rc"
      - "C:/Program Files (x86)/automata/rc.com"
      - "C:/Program Files (x86)/automata/rc.exe"
      - "C:/Program Files (x86)/automata/rc"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Python312\\Scripts"
        - "C:\\Python312"
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\WINDOWS\\System32\\OpenSSH"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\dotnet"
        - "C:\\MinGW\\bin"
        - "C:\\Program Files\\ffmpeg-6.1.1-essentials_build\\bin"
        - "C:\\Program Files\\nodejs"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files\\Graphviz\\bin"
        - "C:\\maven\\apache-maven-3.9.9-bin\\apache-maven-3.9.9\\bin"
        - "C:\\Users\\<USER>\\.jdks\\corretto-17.0.8\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\WINDOWS\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ghcup\\bin"
        - "C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.3\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/automata"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/automata"
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/C++/automata/CMakeFiles/CMakeScratch/TryCompile-bpuf9n"
      binary: "C:/Users/<USER>/OneDrive/Desktop/C++/automata/CMakeFiles/CMakeScratch/TryCompile-bpuf9n"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Ob0 /Od"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/C++/automata/CMakeFiles/CMakeScratch/TryCompile-bpuf9n'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_286c8.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.10+8b8e13593 for .NET Framework
        Build started 2025-08-30 2:47:33 PM.
        
        Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\C++\\automata\\CMakeFiles\\CMakeScratch\\TryCompile-bpuf9n\\cmTC_286c8.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_286c8.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive\\Desktop\\C++\\automata\\CMakeFiles\\CMakeScratch\\TryCompile-bpuf9n\\Debug\\".
          Creating directory "cmTC_286c8.dir\\Debug\\cmTC_286c8.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_286c8.dir\\Debug\\cmTC_286c8.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_286c8.dir\\Debug\\cmTC_286c8.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Fo"cmTC_286c8.dir\\Debug\\\\" /Fd"cmTC_286c8.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35208 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Fo"cmTC_286c8.dir\\Debug\\\\" /Fd"cmTC_286c8.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\OneDrive\\Desktop\\C++\\automata\\CMakeFiles\\CMakeScratch\\TryCompile-bpuf9n\\Debug\\cmTC_286c8.exe" /INCREMENTAL /ILK:"cmTC_286c8.dir\\Debug\\cmTC_286c8.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive/Desktop/C++/automata/CMakeFiles/CMakeScratch/TryCompile-bpuf9n/Debug/cmTC_286c8.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/OneDrive/Desktop/C++/automata/CMakeFiles/CMakeScratch/TryCompile-bpuf9n/Debug/cmTC_286c8.lib" /MACHINE:X64  /machine:x64 cmTC_286c8.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_286c8.vcxproj -> C:\\Users\\<USER>\\OneDrive\\Desktop\\C++\\automata\\CMakeFiles\\CMakeScratch\\TryCompile-bpuf9n\\Debug\\cmTC_286c8.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_286c8.dir\\Debug\\cmTC_286c8.tlog\\unsuccessfulbuild".
          Touching "cmTC_286c8.dir\\Debug\\cmTC_286c8.tlog\\cmTC_286c8.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\C++\\automata\\CMakeFiles\\CMakeScratch\\TryCompile-bpuf9n\\cmTC_286c8.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.65
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35208.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...
